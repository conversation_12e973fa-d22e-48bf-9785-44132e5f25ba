<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Minimal Audio Test</title>
  <style>
    #miniPlayerTest {
      position: fixed;
      bottom: 20px;
      left: 20px;
      background: #222;
      color: #fff;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 2px 10px #0008;
      z-index: 9999;
    }
    #miniPlayerTest button {
      margin-right: 10px;
    }
  </style>
</head>
<body>
  <div id="miniPlayerTest">
    <button id="playTestBtn">Play Test Audio</button>
    <span id="audioStatus">Idle</span>
    <audio id="audioTest" preload="none"></audio>
  </div>
  <script>
    // Known-good public MP3 file
    const previewUrl = "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3";
    const playBtn = document.getElementById('playTestBtn');
    const audio = document.getElementById('audioTest');
    const status = document.getElementById('audioStatus');

    playBtn.addEventListener('click', () => {
      audio.src = previewUrl;
      audio.currentTime = 0;
      const playPromise = audio.play();
      if (playPromise !== undefined) {
        playPromise.then(() => {
          status.textContent = 'Playing...';
        }).catch((err) => {
          status.textContent = 'Error: ' + err.message;
        });
      } else {
        status.textContent = 'Playing...';
      }
    });
    audio.addEventListener('ended', () => {
      status.textContent = 'Ended';
    });
    audio.addEventListener('pause', () => {
      if (!audio.ended) status.textContent = 'Paused';
    });
    audio.addEventListener('play', () => {
      status.textContent = 'Playing...';
    });
    audio.addEventListener('error', () => {
      status.textContent = 'Error: Could not play audio.';
    });
  </script>

  <!-- Mini Player -->
  <div class="mini-player hidden" id="miniPlayer">
      <div class="mini-player-content">
          <div class="mini-player-info">
              <img src="imgs/album-01.png" alt="Current Track" class="mini-player-artwork" id="miniPlayerArtwork">
              <div class="mini-player-text">
                  <h4 id="miniPlayerTitle">Track Title</h4>
                  <p id="miniPlayerArtist">Artist Name</p>
              </div>
          </div>

          <div class="mini-player-controls">
              <button type="button" class="mini-control-btn" id="miniPrevBtn" aria-label="Previous track">
                  <i class="fas fa-step-backward"></i>
              </button>
              <button type="button" class="mini-control-btn play-pause-btn" id="miniPlayPauseBtn" aria-label="Play/Pause">
                  <i class="fas fa-play"></i>
              </button>
              <button type="button" class="mini-control-btn" id="miniNextBtn" aria-label="Next track">
                  <i class="fas fa-step-forward"></i>
              </button>
          </div>

          <div class="mini-player-progress">
              <div class="progress-bar">
                  <div class="progress-fill" id="miniProgressFill"></div>
              </div>
              <div class="time-display">
                  <span id="miniCurrentTime">0:00</span>
                  <span id="miniDuration">3:45</span>
              </div>
          </div>

          <div class="mini-player-actions">
              <button type="button" class="mini-control-btn" id="miniVolumeBtn" aria-label="Volume">
                  <i class="fas fa-volume-up"></i>
              </button>
              <button type="button" class="mini-control-btn" id="miniExpandBtn" aria-label="Expand player">
                  <i class="fas fa-expand"></i>
              </button>
              <button type="button" class="mini-control-btn" id="miniCloseBtn" aria-label="Close player">
                  <i class="fas fa-times"></i>
              </button>
          </div>
      </div>
  </div>

  <script src="js/utils.js"></script>
  <script src="js/main.js"></script>
</body>
</html>
